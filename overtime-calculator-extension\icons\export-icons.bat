@echo off
setlocal
cd /d "%~dp0"

echo 导出 PNG 图标...

if not exist icon.svg (
  echo 未找到 icon.svg
  exit /b 1
)

where magick >nul 2>nul
if %ERRORLEVEL%==0 (
  magick icon.svg -resize 16x16   icon16.png
  magick icon.svg -resize 32x32   icon32.png
  magick icon.svg -resize 48x48   icon48.png
  magick icon.svg -resize 128x128 icon128.png
  echo 使用 ImageMagick 导出完成。
  exit /b 0
)

where inkscape >nul 2>nul
if %ERRORLEVEL%==0 (
  inkscape icon.svg -o icon16.png  -w 16  -h 16
  inkscape icon.svg -o icon32.png  -w 32  -h 32
  inkscape icon.svg -o icon48.png  -w 48  -h 48
  inkscape icon.svg -o icon128.png -w 128 -h 128
  echo 使用 Inkscape 导出完成。
  exit /b 0
)

echo 未检测到 ImageMagick 或 Inkscape，请安装其一后重试。
exit /b 1


