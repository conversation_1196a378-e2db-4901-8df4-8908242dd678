# 加班时间计算器 Chrome 扩展

一个智能的浏览器插件，可以自动识别网页中的打卡表格并计算加班时间。

## 功能特点

### 🎯 智能识别
- 自动识别页面中的打卡表格
- 支持多种表格格式
- 智能提取打卡时间数据

### ⏰ 精确计算
- **工作日规则**：8:30-18:00为标准工作时间，支持半小时弹性打卡
- **加班统计**：下班卡超过18:30才统计加班时间
- **周末规则**：全天算加班，自动扣除午休时间（12:00-13:00）
- **异常处理**：智能处理缺卡、异常打卡等情况

### 🎨 美观界面
- 现代化设计风格
- 渐变背景和动画效果
- 响应式布局，支持移动端
- 悬浮显示，不影响原页面

### ⚡ 便捷操作
- 点击插件图标即可使用
- 支持快捷键操作
- 结果可一键清除
- 多次计算自动更新

## 安装方法

### 方法一：开发者模式安装（推荐）

1. **打开Chrome扩展管理页面**
   - 在Chrome地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 在页面右上角打开"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `overtime-calculator-extension` 文件夹
   - 点击"选择文件夹"

4. **完成安装**
   - 扩展会出现在Chrome工具栏中
   - 可以固定到工具栏方便使用

### 方法二：打包安装

1. 在扩展管理页面点击"打包扩展程序"
2. 选择 `overtime-calculator-extension` 文件夹
3. 生成 `.crx` 文件
4. 拖拽 `.crx` 文件到扩展管理页面安装

## 使用方法

### 🖱️ 界面操作
1. 打开包含打卡表格的网页
2. 点击浏览器工具栏中的插件图标
3. 在弹出窗口中点击"开始计算加班时间"
4. 查看页面右下角显示的计算结果

### ⌨️ 快捷键操作
- `Ctrl + Shift + O`：快速计算加班时间
- `Ctrl + Shift + C`：清除计算结果

### 📊 支持的表格格式
插件会自动识别包含以下列头的表格：
- 打卡日期 / 日期
- 星期 / 周
- 第一次、第二次...（打卡时间列）

时间格式支持：`HH:MM:SS`（如：08:30:15）

## 计算规则详解

### 工作日（周一至周五）
- **标准工作时间**：8:30 - 18:00
- **弹性打卡**：允许半小时弹性，如9:00上班则18:30下班
- **加班判定**：下班卡必须超过18:30才统计加班
- **加班计算**：从18:30开始计算，减去早上弹性迟到时间

**示例**：
- 8:45上班，20:30下班 → 加班1小时45分钟（20:30-18:30-15分钟弹性）
- 8:15上班，18:30下班 → 无加班（未超过18:30）

### 周末（周六、周日）
- **全天加班**：所有工作时间都算加班
- **午休扣除**：自动扣除12:00-13:00午休时间
- **最少打卡**：需要至少2次打卡记录

**示例**：
- 周六 9:00-17:00 → 加班7小时（8小时-1小时午休）

### 异常情况处理
- 第一次打卡 > 12:00 → 不统计加班
- 只有上班卡，无下班卡 → 不统计加班
- 下班卡 < 18:30（工作日）→ 不统计加班

## 技术特性

- **兼容性**：支持Chrome 88+版本
- **权限最小化**：仅需要activeTab权限
- **性能优化**：轻量级设计，不影响页面性能
- **安全性**：所有计算在本地进行，不上传数据

## 故障排除

### 插件无法识别表格
- 检查表格是否包含"日期"、"星期"等关键列头
- 确保时间格式为 HH:MM:SS
- 刷新页面后重试

### 计算结果不准确
- 检查打卡时间格式是否正确
- 确认表格数据完整性
- 查看控制台是否有错误信息

### 插件无法加载
- 确认Chrome版本 ≥ 88
- 检查开发者模式是否开启
- 重新加载扩展程序

## 更新日志

### v1.0.4 (2025-08-19)
- ⚡ 性能优化：移除全站 `content_scripts` 注入，改为按需注入 `calculator.js`，显著提升弹窗打开速度
- 🔒 权限收敛：新增 `host_permissions` 仅匹配 `https://app.mindray.com/EmpSelfHelp/Attendance/Index*`
- 🧹 代码清理：删除未使用的 `content.js`、`content.css`；在 `calculator.js` 内联结果面板动画与滚动条样式
- 🖼️ 图标兼容：manifest 切换为使用 PNG 图标（16/32/48/128）确保 Chrome 应用显示

### v1.0.2 (2025-08-19)
- 🎨 简化图标系统：直接使用SVG图标，移除PNG生成器
- 🗑️ 删除不必要的图标生成文件
- 📝 更新安装说明

### v1.0.1 (2025-08-19)
- 🔧 调整加班计算规则：从18:30开始算加班（原19:00）
- 📝 更新相关说明文档

### v1.0.0 (2025-08-19)
- 🎉 首次发布
- ✨ 支持工作日和周末不同计算规则
- 🎨 美观的界面设计
- ⚡ 快捷键支持
- 📱 移动端适配

## 开发者信息

如需定制或有问题反馈，请联系开发者。

## 许可证

本项目仅供个人学习和使用。
