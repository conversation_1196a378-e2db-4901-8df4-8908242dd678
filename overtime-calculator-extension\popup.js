document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculateBtn');
    const clearBtn = document.getElementById('clearBtn');
    const status = document.getElementById('status');

    // 显示状态信息
    function showStatus(message, type = 'info') {
        status.textContent = message;
        status.className = `status ${type}`;
        status.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(() => {
            status.style.display = 'none';
        }, 3000);
    }

    const TARGET_PREFIX = 'https://app.mindray.com/EmpSelfHelp/Attendance/Index';
    function isSupportedUrl(url) {
        return typeof url === 'string' && url.startsWith(TARGET_PREFIX);
    }

    // 快速检查页面是否有gridtable元素
    async function quickCheck(tabId) {
        try {
            const checkResults = await chrome.scripting.executeScript({
                target: { tabId: tabId },
                function: () => {
                    // 快速查找gridtable
                    let gridTable = document.getElementById('gridtable');
                    if (!gridTable) {
                        const iframes = document.querySelectorAll('iframe');
                        for (let iframe of iframes) {
                            try {
                                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                gridTable = iframeDoc.getElementById('gridtable');
                                if (gridTable) break;
                            } catch (e) { continue; }
                        }
                    }
                    return {
                        hasGridTable: !!gridTable,
                        hasTable: gridTable ? !!gridTable.querySelector('table') : false
                    };
                }
            });
            return checkResults[0].result;
        } catch (error) {
            return { hasGridTable: false, hasTable: false };
        }
    }

    // 计算加班时间
    calculateBtn.addEventListener('click', async function() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!isSupportedUrl(tab.url)) {
                showStatus('请在考勤页面使用（https://app.mindray.com/EmpSelfHelp/Attendance/Index）', 'error');
                return;
            }

            showStatus('正在计算加班时间...', 'info');
            calculateBtn.disabled = true;

            // 快速检查
            const pageCheck = await quickCheck(tab.id);

            if (!pageCheck.hasGridTable) {
                showStatus('❌ 页面中找不到id为gridtable的元素', 'error');
                return;
            }

            if (!pageCheck.hasTable) {
                showStatus('❌ gridtable元素下找不到表格', 'error');
                return;
            }

            // 注入并执行计算脚本
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['calculator.js']
            });

            const calcResults = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: () => calculateOvertimeInPage()
            });

            const result = calcResults[0].result;

            if (result && result.success) {
                showStatus(`✅ 计算完成！总加班: ${result.totalHours}小时${result.totalMinutes}分钟`, 'success');
            } else {
                showStatus(`❌ ${result ? result.error : '计算失败'}`, 'error');
            }

        } catch (error) {
            console.error('执行脚本时出错:', error);
            showStatus('❌ 执行失败，请刷新页面后重试', 'error');
        } finally {
            calculateBtn.disabled = false;
        }
    });

    // 清除结果
    clearBtn.addEventListener('click', async function() {
        try {
            showStatus('正在清除结果...', 'info');
            clearBtn.disabled = true;

            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!isSupportedUrl(tab.url)) {
                showStatus('请在考勤页面使用（https://app.mindray.com/EmpSelfHelp/Attendance/Index）', 'error');
                return;
            }

            // 注入清除函数并执行
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['calculator.js']
            });

            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: () => clearOvertimeResults()
            });

            showStatus('🗑️ 结果已清除', 'success');

        } catch (error) {
            console.error('清除结果时出错:', error);
            showStatus('❌ 清除失败', 'error');
        } finally {
            clearBtn.disabled = false;
        }
    });

    // 说明：为减少弹窗首开阶段的潜在延迟，这里不做任何 Chrome API 的预检查。
    // 在点击按钮时再做 URL 校验与脚本注入。
});