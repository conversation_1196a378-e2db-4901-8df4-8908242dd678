<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>导出 PNG 图标</title>
  <style>
    body { font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Microsoft YaHei',Arial,sans-serif; padding: 20px; }
    button { padding: 10px 16px; font-size: 14px; }
    .tip { margin-top: 10px; color: #555; }
  </style>
</head>
<body>
  <h3>从 icon.svg 导出 PNG 图标</h3>
  <p>点击按钮后将生成并下载 16/32/48/128 四个尺寸的 PNG 文件。</p>
  <button id="exportBtn">导出 PNG 图标</button>
  <div class="tip">下载完成后，请将文件移动到本目录（icons）并命名为 icon16.png、icon32.png、icon48.png、icon128.png。</div>

  <script>
    const sizes = [16, 32, 48, 128];

    async function loadSvgAsImage(url) {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = url;
      });
    }

    function downloadCanvas(canvas, filename) {
      canvas.toBlob(blob => {
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        setTimeout(() => {
          URL.revokeObjectURL(a.href);
          a.remove();
        }, 1000);
      }, 'image/png');
    }

    async function exportPngs() {
      try {
        const img = await loadSvgAsImage('icon.svg');
        for (const size of sizes) {
          const canvas = document.createElement('canvas');
          canvas.width = size;
          canvas.height = size;
          const ctx = canvas.getContext('2d');
          ctx.clearRect(0, 0, size, size);
          ctx.drawImage(img, 0, 0, size, size);
          downloadCanvas(canvas, `icon${size}.png`);
        }
        alert('导出完成，请到下载目录查收并移动到 icons 文件夹。');
      } catch (e) {
        alert('导出失败：' + e);
      }
    }

    document.getElementById('exportBtn').addEventListener('click', exportPngs);
  </script>
</body>
<\html>


